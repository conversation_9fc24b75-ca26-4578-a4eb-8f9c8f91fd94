import { Head, router } from '@inertiajs/react';
import { useMemo } from 'react';

interface Team {
    id: number;
    name: string;
}

interface Match {
    home_team: Team;
    away_team: Team;
    week: number;
    home_score?: number;
    away_score?: number;
    is_played?: boolean;
}

interface Standing {
    team: string;
    played: number;
    won: number;
    drawn: number;
    lost: number;
    gf: number;
    ga: number;
    gd: number;
    points: number;
}

interface SimulationProps {
    fixtures: Match[];
    seasonId: string;
}

export default function Simulation({ fixtures, seasonId }: SimulationProps) {
    // Group fixtures by week
    const fixturesByWeek = useMemo(() => {
        return fixtures.reduce((acc, match) => {
            if (!acc[match.week]) {
                acc[match.week] = [];
            }
            acc[match.week].push(match);
            return acc;
        }, {} as Record<number, Match[]>);
    }, [fixtures]);

    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));

    // Calculate standings
    const standings = useMemo(() => {
        const teamStats: Record<string, Standing> = {};

        // Initialize all teams
        fixtures.forEach(match => {
            if (!teamStats[match.home_team.name]) {
                teamStats[match.home_team.name] = {
                    team: match.home_team.name,
                    played: 0,
                    won: 0,
                    drawn: 0,
                    lost: 0,
                    gf: 0,
                    ga: 0,
                    gd: 0,
                    points: 0
                };
            }
            if (!teamStats[match.away_team.name]) {
                teamStats[match.away_team.name] = {
                    team: match.away_team.name,
                    played: 0,
                    won: 0,
                    drawn: 0,
                    lost: 0,
                    gf: 0,
                    ga: 0,
                    gd: 0,
                    points: 0
                };
            }
        });

        // Calculate stats from played matches
        fixtures.filter(match => match.is_played).forEach(match => {
            const homeTeam = teamStats[match.home_team.name];
            const awayTeam = teamStats[match.away_team.name];

            homeTeam.played++;
            awayTeam.played++;

            homeTeam.gf += match.home_score || 0;
            homeTeam.ga += match.away_score || 0;
            awayTeam.gf += match.away_score || 0;
            awayTeam.ga += match.home_score || 0;

            if ((match.home_score || 0) > (match.away_score || 0)) {
                homeTeam.won++;
                homeTeam.points += 3;
                awayTeam.lost++;
            } else if ((match.home_score || 0) < (match.away_score || 0)) {
                awayTeam.won++;
                awayTeam.points += 3;
                homeTeam.lost++;
            } else {
                homeTeam.drawn++;
                awayTeam.drawn++;
                homeTeam.points += 1;
                awayTeam.points += 1;
            }
        });

        // Calculate goal difference and sort
        const standingsArray = Object.values(teamStats).map(team => ({
            ...team,
            gd: team.gf - team.ga
        }));

        return standingsArray.sort((a, b) => {
            if (a.points !== b.points) return b.points - a.points;
            return b.gd - a.gd;
        });
    }, [fixtures]);

    // Calculate championship predictions (simple based on current points)
    const predictions = useMemo(() => {
        const totalPoints = standings.reduce((sum, team) => sum + team.points, 0);
        const predictions: Record<string, number> = {};

        if (totalPoints === 0) {
            // Equal chances at start
            standings.forEach(team => {
                predictions[team.team] = Math.round(100 / standings.length);
            });
        } else {
            standings.forEach((team, index) => {
                // Leader gets higher chance, others get proportional
                const baseChance = totalPoints > 0 ? (team.points / totalPoints) * 100 : 25;
                const positionBonus = (standings.length - index) * 5;
                predictions[team.team] = Math.min(Math.round(baseChance + positionBonus), 100);
            });
        }

        return predictions;
    }, [standings]);
    
    const handlePlayAllWeeks = () => {
        router.post('/play-all-weeks', { season_id: seasonId });
    };

    const handlePlayNextWeek = () => {
        router.post('/play-next-week', { season_id: seasonId });
    };

    return (
        <>
            <Head title="League Simulation" />
            <div className="min-h-screen bg-gray-100 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold text-gray-900">League Simulation</h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Current season standings, fixtures, and championship predictions
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Left Column - Standings */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200">
                                    <h2 className="text-lg font-medium text-gray-900">League Table</h2>
                                </div>
                                <div className="p-4">
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full text-xs">
                                            <thead>
                                                <tr className="border-b border-gray-200">
                                                    <th className="text-left py-2 font-medium text-gray-700">Pos</th>
                                                    <th className="text-left py-2 font-medium text-gray-700">Team</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">P</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">W</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">D</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">L</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">GF</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">GA</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">GD</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">Pts</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {standings.map((team, index) => (
                                                    <tr key={team.team} className="border-b border-gray-100">
                                                        <td className="py-2 text-gray-900 font-medium">{index + 1}</td>
                                                        <td className="py-2 text-gray-900">{team.team}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.played}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.won}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.drawn}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.lost}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.gf}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.ga}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.gd > 0 ? '+' : ''}{team.gd}</td>
                                                        <td className="py-2 text-center text-gray-900 font-bold">{team.points}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Middle Column - Fixtures */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                                    <h2 className="text-lg font-medium text-gray-900">Fixtures</h2>
                                    <div className="flex space-x-2">
                                        <button
                                            onClick={handlePlayNextWeek}
                                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                                        >
                                            Play Next Week
                                        </button>
                                        <button
                                            onClick={handlePlayAllWeeks}
                                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                                        >
                                            Play All Weeks
                                        </button>
                                    </div>
                                </div>
                                <div className="p-4 max-h-96 overflow-y-auto">
                                    {weekNumbers.map((weekNumber) => (
                                        <div key={weekNumber} className="mb-4">
                                            <h3 className="text-sm font-medium text-gray-900 mb-2">
                                                Week {weekNumber}
                                            </h3>
                                            <div className="space-y-2">
                                                {fixturesByWeek[weekNumber].map((match, index) => (
                                                    <div
                                                        key={index}
                                                        className="bg-gray-50 rounded p-2 text-sm"
                                                    >
                                                        <div className="flex items-center justify-between">
                                                            <span className="text-gray-900">{match.home_team.name}</span>
                                                            <div className="px-2">
                                                                {match.is_played ? (
                                                                    <span className="font-bold text-gray-900">
                                                                        {match.home_score} - {match.away_score}
                                                                    </span>
                                                                ) : (
                                                                    <span className="text-gray-400">vs</span>
                                                                )}
                                                            </div>
                                                            <span className="text-gray-900">{match.away_team.name}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Championship Predictions */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200">
                                    <h2 className="text-lg font-medium text-gray-900">Championship Predictions</h2>
                                </div>
                                <div className="p-4">
                                    <div className="space-y-3">
                                        {Object.entries(predictions)
                                            .sort(([,a], [,b]) => b - a)
                                            .map(([team, percentage]) => (
                                            <div key={team} className="flex items-center justify-between">
                                                <span className="text-sm text-gray-900">{team}</span>
                                                <div className="flex items-center">
                                                    <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{ width: `${percentage}%` }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-sm font-medium text-gray-900 w-8">
                                                        {percentage}%
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6">
                        <a
                            href="/"
                            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
                        >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Back to Teams
                        </a>
                    </div>
                </div>
            </div>
        </>
    );
}
