<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LeagueController extends Controller
{
    public function getTeams()
    {
        $teams = Team::pluck('name')->toArray();

        return response()->json([
            'data' => $teams
        ]);
    }

    public function generateFixture(FixtureGenerator $generator)
    {
        $teamIds = Team::pluck('id')->toArray();

        if (count($teamIds) < 2) {
            return response()->json(['error' => 'At least 2 teams required to generate fixtures.'], 400);
        }

        $seasonId = Str::random(8);
        $fixture = $generator->generate($teamIds, $seasonId);

        return response()->json([
            'fixtures' => LeagueMatchResource::collection($fixture),
            'seasonId' => $seasonId
        ]);
    }

    public function startSimulation(FixtureGenerator $generator)
    {
        $teamIds = Team::pluck('id')->toArray();

        if (count($teamIds) < 2) {
            return back()->with('error', 'At least 2 teams required to generate fixtures.');
        }

        $seasonId = Str::random(8);
        $fixture = $generator->generate($teamIds, $seasonId);

        return Inertia::render('Simulation', [
            'fixtures' => LeagueMatchResource::collection($fixture),
            'seasonId' => $seasonId
        ]);
    }

    public function simulationIndex()
    {
        // Get the latest season
        $latestSeasonId = LeagueMatches::latest()->value('season_id');

        if (!$latestSeasonId) {
            return redirect()->route('home')->with('error', 'No fixtures found. Please generate fixtures first.');
        }

        // Get all fixtures for the latest season
        $fixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $latestSeasonId)
            ->orderBy('week')
            ->orderBy('id')
            ->get();

        return Inertia::render('Simulation', [
            'fixtures' => LeagueMatchResource::collection($fixtures),
            'seasonId' => $latestSeasonId
        ]);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'played_count' => $updateCount,
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $matches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        foreach ($matches as $match) {
            $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'message' => "Week {$nextWeek} matches played.",
            'played_count' => $matches->count(),
            'week' => $nextWeek
        ]);
    }

    // Inertia Pages
    public function teamsIndex()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }

    /**
     * @param mixed $match
     * @return bool
     */
    public function simulateMatchAsPlayed(mixed $match): bool
    {
        return $match->update([
            'home_score' => rand(0, 5),
            'away_score' => rand(0, 5),
            'is_played' => true,
            'played_at' => now(),
        ]);
    }
}
